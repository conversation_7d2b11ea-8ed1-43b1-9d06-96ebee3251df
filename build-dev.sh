#!/bin/bash

# GameFlex Mobile - Development Build Script
# This script builds the app for development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 -p <platform>"
    echo "Platforms: android, ios, web, all"
    echo "Example: $0 -p ios"
    exit 1
}

# Parse command line arguments
while getopts "p:" opt; do
    case $opt in
        p)
            PLATFORM="$OPTARG"
            ;;
        \?)
            echo "Invalid option: -$OPTARG" >&2
            show_usage
            ;;
    esac
done

# Check if platform is provided
if [ -z "$PLATFORM" ]; then
    show_usage
fi

# Validate platform
case $PLATFORM in
    android|ios|web|all)
        ;;
    *)
        print_error "Invalid platform: $PLATFORM"
        show_usage
        ;;
esac

print_status "🚀 Building GameFlex Mobile for DEVELOPMENT"
print_status "Platform: $PLATFORM"
echo

# Clean previous builds
print_status "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
print_status "📦 Getting dependencies..."
flutter pub get

# Build for specific platform
case $PLATFORM in
    "android")
        print_status "🏗️ Building for Android (development)..."
        flutter build apk --debug --dart-define=DEVELOPMENT=true
        ;;
    "ios")
        print_status "🏗️ Building for iOS (development)..."
        
        # Restore Release as default configuration for Xcode
        print_status "🔧 Restoring Release as default configuration for Xcode..."
        sed -i.bak 's/defaultConfigurationName = Staging;/defaultConfigurationName = Release;/g' ios/Runner.xcodeproj/project.pbxproj
        
        flutter build ios --debug --dart-define=DEVELOPMENT=true
        
        print_success "✅ Development build completed! Xcode is now set to default to Release configuration."
        ;;
    "web")
        print_status "🏗️ Building for Web (development)..."
        flutter build web --dart-define=DEVELOPMENT=true
        ;;
    "all")
        print_status "🏗️ Building for all platforms (development)..."
        
        # Restore Release as default configuration for Xcode
        print_status "🔧 Restoring Release as default configuration for Xcode..."
        sed -i.bak 's/defaultConfigurationName = Staging;/defaultConfigurationName = Release;/g' ios/Runner.xcodeproj/project.pbxproj
        
        flutter build apk --debug --dart-define=DEVELOPMENT=true
        flutter build ios --debug --dart-define=DEVELOPMENT=true
        flutter build web --dart-define=DEVELOPMENT=true
        
        print_success "✅ Development build completed for all platforms! Xcode is now set to default to Release configuration."
        ;;
esac

print_success "✅ Development build completed successfully!"
print_status "📁 Build output: build/ directory"
