#!/usr/bin/env pwsh
# Development Build Script for GameFlex Mobile
# This script builds the app for development with local backend URLs

param(
    [string]$Platform = "windows",
    [switch]$Release = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host "GameFlex Mobile Development Build Script" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage: .\build-dev.ps1 [OPTIONS]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Platform <platform>  Target platform (windows, android, ios, web) [default: windows]"
    Write-Host "  -Release              Build in release mode [default: debug]"
    Write-Host "  -Help                 Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\build-dev.ps1                    # Build Windows debug"
    Write-Host "  .\build-dev.ps1 -Platform android  # Build Android debug"
    Write-Host "  .\build-dev.ps1 -Platform ios      # Build iOS debug"
    Write-Host "  .\build-dev.ps1 -Release           # Build Windows release"
    Write-Host "  .\build-dev.ps1 -Platform android -Release  # Build Android release"
    exit 0
}

Write-Host "🚀 Building GameFlex Mobile for DEVELOPMENT" -ForegroundColor Green
Write-Host "Platform: $Platform" -ForegroundColor Yellow
Write-Host "Mode: $(if ($Release) { 'Release' } else { 'Debug' })" -ForegroundColor Yellow
Write-Host ""

# Ensure we're in the correct directory
if (!(Test-Path "pubspec.yaml")) {
    Write-Host "❌ Error: pubspec.yaml not found. Please run this script from the Flutter project root." -ForegroundColor Red
    exit 1
}

# Clean previous builds
Write-Host "🧹 Cleaning previous builds..." -ForegroundColor Yellow
flutter clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter clean failed" -ForegroundColor Red
    exit 1
}

# Get dependencies
Write-Host "📦 Getting dependencies..." -ForegroundColor Yellow
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Flutter pub get failed" -ForegroundColor Red
    exit 1
}

# Build based on platform
$buildMode = if ($Release) { "release" } else { "debug" }

switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "🏗️ Building for Windows ($buildMode)..." -ForegroundColor Yellow
        if ($Release) {
            flutter build windows --release
        }
        else {
            flutter build windows --debug
        }
    }
    "android" {
        Write-Host "🏗️ Building for Android ($buildMode)..." -ForegroundColor Yellow
        if ($Release) {
            flutter build apk --release --flavor development
        }
        else {
            flutter build apk --debug --flavor development
        }
    }
    "ios" {
        Write-Host "🏗️ Building for iOS ($buildMode)..." -ForegroundColor Yellow

        # Restore Release as default configuration for Xcode
        Write-Host "🔧 Restoring Release as default configuration for Xcode..." -ForegroundColor Yellow
        (Get-Content ios/Runner.xcodeproj/project.pbxproj) -replace 'defaultConfigurationName = Staging;', 'defaultConfigurationName = Release;' | Set-Content ios/Runner.xcodeproj/project.pbxproj

        if ($Release) {
            flutter build ios --release --dart-define=PRODUCTION=false
        }
        else {
            flutter build ios --debug --dart-define=PRODUCTION=false
        }

        Write-Host "✅ Development build completed! Xcode is now set to default to Release configuration." -ForegroundColor Green
    }
    "web" {
        Write-Host "🏗️ Building for Web ($buildMode)..." -ForegroundColor Yellow
        if ($Release) {
            flutter build web --release
        }
        else {
            flutter build web --debug
        }
    }
    default {
        Write-Host "❌ Unsupported platform: $Platform" -ForegroundColor Red
        Write-Host "Supported platforms: windows, android, ios, web" -ForegroundColor Yellow
        exit 1
    }
}

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ Development build completed successfully!" -ForegroundColor Green

# Show build output location
switch ($Platform.ToLower()) {
    "windows" {
        Write-Host "📁 Build output: build\windows\x64\runner\$buildMode\" -ForegroundColor Cyan
    }
    "android" {
        if ($Release) {
            Write-Host "📁 Build output: build\app\outputs\flutter-apk\app-development-release.apk" -ForegroundColor Cyan
        }
        else {
            Write-Host "📁 Build output: build\app\outputs\flutter-apk\app-development-debug.apk" -ForegroundColor Cyan
        }
    }
    "ios" {
        Write-Host "📁 Build output: build\ios\iphoneos\Runner.app (or build\ios\iphonesimulator\Runner.app)" -ForegroundColor Cyan
    }
    "web" {
        Write-Host "📁 Build output: build\web\" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🔧 Development Configuration:" -ForegroundColor Yellow
Write-Host "  - Uses local backend (localhost/********)" -ForegroundColor White
Write-Host "  - Debug logging enabled" -ForegroundColor White
Write-Host "  - Development app name/icon" -ForegroundColor White
